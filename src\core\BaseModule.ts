import { ITradingModule } from "../types";
import { EventBus, EventFactory, ModuleEvent } from "./EventBus";
import { Logger } from "winston";

/**
 * Base class for all trading bot modules
 * Provides common functionality for lifecycle management and event handling
 */
export abstract class BaseModule implements ITradingModule {
  protected eventBus: EventBus;
  protected logger: Logger;
  protected isInitialized: boolean = false;
  protected isStarted: boolean = false;
  protected startTime?: number;
  protected errors: Error[] = [];
  protected maxErrors: number = 10;

  constructor(
    public readonly name: string,
    eventBus: EventBus,
    logger: Logger
  ) {
    this.eventBus = eventBus;
    this.logger = logger.child({ module: name });
    this.setupErrorHandling();
  }

  /**
   * Initialize the module
   * Must be implemented by subclasses
   */
  public async initialize(): Promise<void> {
    try {
      this.logger.info(`Initializing module: ${this.name}`);
      await this.onInitialize();
      this.isInitialized = true;
      this.emitSystemEvent("module_initialized", { module: this.name });
      this.logger.info(`Module initialized: ${this.name}`);
    } catch (error) {
      this.handleError(error as Error, "initialization");
      throw error;
    }
  }

  /**
   * Start the module
   */
  public async start(): Promise<void> {
    if (!this.isInitialized) {
      throw new Error(
        `Module ${this.name} must be initialized before starting`
      );
    }

    if (this.isStarted) {
      this.logger.warn(`Module ${this.name} is already started`);
      return;
    }

    try {
      this.logger.info(`Starting module: ${this.name}`);
      await this.onStart();
      this.isStarted = true;
      this.startTime = Date.now();
      this.emitSystemEvent("module_started", { module: this.name });
      this.logger.info(`Module started: ${this.name}`);
    } catch (error) {
      this.handleError(error as Error, "start");
      throw error;
    }
  }

  /**
   * Stop the module
   */
  public async stop(): Promise<void> {
    if (!this.isStarted) {
      this.logger.warn(`Module ${this.name} is not running`);
      return;
    }

    try {
      this.logger.info(`Stopping module: ${this.name}`);
      await this.onStop();
      this.isStarted = false;
      this.emitSystemEvent("module_stopped", {
        module: this.name,
        uptime: this.getUptime(),
      });
      this.logger.info(`Module stopped: ${this.name}`);
    } catch (error) {
      this.handleError(error as Error, "stop");
      throw error;
    }
  }

  /**
   * Check if module is running
   */
  public isRunning(): boolean {
    return this.isStarted;
  }

  /**
   * Get module status
   */
  public getStatus(): Record<string, any> {
    return {
      name: this.name,
      initialized: this.isInitialized,
      running: this.isStarted,
      uptime: this.getUptime(),
      errorCount: this.errors.length,
      lastError:
        this.errors.length > 0
          ? this.errors[this.errors.length - 1]?.message
          : null,
      ...this.getModuleSpecificStatus(),
    };
  }

  /**
   * Get module uptime in milliseconds
   */
  public getUptime(): number {
    return this.startTime ? Date.now() - this.startTime : 0;
  }

  /**
   * Get recent errors
   */
  public getErrors(): Error[] {
    return [...this.errors];
  }

  /**
   * Clear error history
   */
  public clearErrors(): void {
    this.errors = [];
    this.logger.info(`Error history cleared for module: ${this.name}`);
  }

  /**
   * Abstract methods to be implemented by subclasses
   */
  protected abstract onInitialize(): Promise<void>;
  protected abstract onStart(): Promise<void>;
  protected abstract onStop(): Promise<void>;
  protected abstract getModuleSpecificStatus(): Record<string, any>;

  /**
   * Handle errors with logging and event emission
   */
  protected handleError(error: Error, context: string): void {
    this.logger.error(`Error in module ${this.name} during ${context}`, {
      error: error.message,
      stack: error.stack,
      context,
    });

    // Add to error history
    this.errors.push(error);
    if (this.errors.length > this.maxErrors) {
      this.errors = this.errors.slice(-this.maxErrors);
    }

    // Emit error event
    this.emitSystemEvent("module_error", {
      module: this.name,
      error: error.message,
      context,
    });
  }

  /**
   * Emit system events
   */
  protected emitSystemEvent(type: string, data: any): void {
    const event = EventFactory.createSystemEvent(
      type as ModuleEvent,
      data,
      this.name
    );
    this.eventBus.emitTradingEvent(event);
  }

  /**
   * Setup error handling for uncaught errors
   */
  private setupErrorHandling(): void {
    process.on("uncaughtException", (error: Error) => {
      this.handleError(error, "uncaughtException");
    });

    process.on("unhandledRejection", (reason: any) => {
      const error =
        reason instanceof Error ? reason : new Error(String(reason));
      this.handleError(error, "unhandledRejection");
    });
  }

  /**
   * Graceful shutdown handler
   */
  protected async gracefulShutdown(): Promise<void> {
    this.logger.info(`Graceful shutdown initiated for module: ${this.name}`);

    try {
      if (this.isStarted) {
        await this.stop();
      }
    } catch (error) {
      this.logger.error(
        `Error during graceful shutdown of module ${this.name}`,
        {
          error: (error as Error).message,
        }
      );
    }
  }

  /**
   * Health check method
   */
  public async healthCheck(): Promise<boolean> {
    try {
      return (
        this.isInitialized &&
        this.isStarted &&
        (await this.performHealthCheck())
      );
    } catch (error) {
      this.handleError(error as Error, "healthCheck");
      return false;
    }
  }

  /**
   * Module-specific health check to be implemented by subclasses
   */
  protected async performHealthCheck(): Promise<boolean> {
    return true; // Default implementation
  }

  /**
   * Restart the module
   */
  public async restart(): Promise<void> {
    this.logger.info(`Restarting module: ${this.name}`);

    if (this.isStarted) {
      await this.stop();
    }

    await this.start();
  }
}

/**
 * Module manager for handling multiple modules
 */
export class ModuleManager {
  private modules: Map<string, BaseModule> = new Map();
  private logger: Logger;
  private eventBus: EventBus;

  constructor(eventBus: EventBus, logger: Logger) {
    this.eventBus = eventBus;
    this.logger = logger.child({ component: "ModuleManager" });
  }

  /**
   * Register a module
   */
  public registerModule(module: BaseModule): void {
    if (this.modules.has(module.name)) {
      throw new Error(`Module ${module.name} is already registered`);
    }

    this.modules.set(module.name, module);
    this.logger.info(`Module registered: ${module.name}`);
    console.log(this.eventBus);
  }

  /**
   * Initialize all modules
   */
  public async initializeAll(): Promise<void> {
    this.logger.info("Initializing all modules");

    for (const [name, module] of this.modules) {
      try {
        await module.initialize();
      } catch (error) {
        this.logger.error(`Failed to initialize module ${name}`, {
          error: (error as Error).message,
        });
        throw error;
      }
    }
  }

  /**
   * Start all modules
   */
  public async startAll(): Promise<void> {
    this.logger.info("Starting all modules");

    for (const [name, module] of this.modules) {
      try {
        await module.start();
      } catch (error) {
        this.logger.error(`Failed to start module ${name}`, {
          error: (error as Error).message,
        });
        throw error;
      }
    }
  }

  /**
   * Stop all modules
   */
  public async stopAll(): Promise<void> {
    this.logger.info("Stopping all modules");

    // Stop modules in reverse order
    const moduleArray = Array.from(this.modules.entries()).reverse();

    for (const [name, module] of moduleArray) {
      try {
        await module.stop();
      } catch (error) {
        this.logger.error(`Failed to stop module ${name}`, {
          error: (error as Error).message,
        });
      }
    }
  }

  /**
   * Get module by name
   */
  public getModule<T extends BaseModule>(name: string): T | undefined {
    return this.modules.get(name) as T;
  }

  /**
   * Get all module statuses
   */
  public getAllStatuses(): Record<string, any> {
    const statuses: Record<string, any> = {};

    for (const [name, module] of this.modules) {
      statuses[name] = module.getStatus();
    }

    return statuses;
  }

  /**
   * Perform health check on all modules
   */
  public async healthCheckAll(): Promise<Record<string, boolean>> {
    const results: Record<string, boolean> = {};

    for (const [name, module] of this.modules) {
      results[name] = await module.healthCheck();
    }

    return results;
  }
}
