import { Order, Ticker, Trade, OHLCV, OrderBook } from 'ccxt';

// Core Trading Types
export interface TradingPair {
  symbol: string;
  base: string;
  quote: string;
  active: boolean;
  precision: {
    amount: number;
    price: number;
  };
  limits: {
    amount: {
      min: number;
      max: number;
    };
    price: {
      min: number;
      max: number;
    };
  };
}

export interface Position {
  id: string;
  symbol: string;
  side: 'long' | 'short';
  size: number;
  entryPrice: number;
  currentPrice: number;
  unrealizedPnl: number;
  realizedPnl: number;
  timestamp: number;
  stopLoss?: number;
  takeProfit?: number;
}

export interface Portfolio {
  totalValue: number;
  availableBalance: number;
  positions: Position[];
  exposure: number;
  drawdown: number;
  maxDrawdown: number;
}

// Strategy Types
export interface StrategyParameters {
  [key: string]: number | string | boolean;
}

export interface StrategySignal {
  symbol: string;
  action: 'buy' | 'sell' | 'hold';
  confidence: number;
  price?: number;
  quantity?: number;
  stopLoss?: number;
  takeProfit?: number;
  timestamp: number;
  metadata?: Record<string, any>;
}

export interface StrategyConfig {
  name: string;
  symbol: string;
  timeframe: string;
  parameters: StrategyParameters;
  enabled: boolean;
  riskPerTrade: number;
  maxPositionSize: number;
}

// Market Data Types
export interface MarketData {
  ticker: Ticker;
  orderBook: OrderBook;
  trades: Trade[];
  ohlcv: OHLCV[];
  timestamp: number;
}

export interface CandleData {
  timestamp: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

// Order Types
export interface OrderRequest {
  symbol: string;
  type: 'market' | 'limit' | 'stop' | 'stop-limit';
  side: 'buy' | 'sell';
  amount: number;
  price?: number;
  stopPrice?: number;
  timeInForce?: 'GTC' | 'IOC' | 'FOK';
  clientOrderId?: string;
  metadata?: Record<string, any>;
}

export interface OrderResult extends Order {
  executionTime: number;
  slippage?: number;
  fees: {
    currency: string;
    cost: number;
    rate: number;
  };
}

// Risk Management Types
export interface RiskLimits {
  maxPositionSize: number;
  maxDrawdown: number;
  maxPortfolioExposure: number;
  riskPerTrade: number;
  maxDailyLoss: number;
  maxConcurrentPositions: number;
}

export interface RiskMetrics {
  currentDrawdown: number;
  portfolioExposure: number;
  dailyPnl: number;
  openPositions: number;
  riskScore: number;
}

// Event Types
export interface TradingEvent {
  type: string;
  timestamp: number;
  data: any;
  source: string;
}

export interface MarketDataEvent extends TradingEvent {
  type: 'ticker' | 'orderbook' | 'trade' | 'ohlcv';
  symbol: string;
  data: Ticker | OrderBook | Trade | OHLCV;
}

export interface OrderEvent extends TradingEvent {
  type: 'order_created' | 'order_filled' | 'order_canceled' | 'order_failed';
  orderId: string;
  data: OrderResult;
}

export interface StrategyEvent extends TradingEvent {
  type: 'signal_generated' | 'strategy_started' | 'strategy_stopped' | 'strategy_error';
  strategyName: string;
  data: StrategySignal | Error;
}

// Configuration Types
export interface ExchangeConfig {
  apiKey: string;
  secret: string;
  sandbox: boolean;
  testnet: boolean;
  rateLimit: number;
  timeout: number;
  enableRateLimit: boolean;
}

export interface TradingConfig {
  exchange: ExchangeConfig;
  trading: {
    maxPositionSize: number;
    maxDrawdown: number;
    riskPerTrade: number;
    maxPortfolioExposure: number;
    enablePaperTrading: boolean;
  };
  strategies: StrategyConfig[];
  logging: {
    level: string;
    filePath: string;
    maxSize: string;
    maxFiles: number;
  };
}

// Performance Analytics Types
export interface PerformanceMetrics {
  totalReturn: number;
  sharpeRatio: number;
  maxDrawdown: number;
  winRate: number;
  profitFactor: number;
  totalTrades: number;
  winningTrades: number;
  losingTrades: number;
  averageWin: number;
  averageLoss: number;
  largestWin: number;
  largestLoss: number;
  consecutiveWins: number;
  consecutiveLosses: number;
  calmarRatio: number;
  sortinoRatio: number;
}

export interface TradeRecord {
  id: string;
  symbol: string;
  side: 'buy' | 'sell';
  amount: number;
  price: number;
  timestamp: number;
  pnl: number;
  fees: number;
  strategy: string;
  metadata?: Record<string, any>;
}

// Error Types
export interface TradingError extends Error {
  code: string;
  type: 'network' | 'api' | 'validation' | 'insufficient_balance' | 'rate_limit' | 'exchange_maintenance';
  retryable: boolean;
  timestamp: number;
  context?: Record<string, any>;
}

// Module Interfaces
export interface ITradingModule {
  name: string;
  initialize(): Promise<void>;
  start(): Promise<void>;
  stop(): Promise<void>;
  isRunning(): boolean;
  getStatus(): Record<string, any>;
}

export interface IEventEmitter {
  on(event: string, listener: (...args: any[]) => void): void;
  emit(event: string, ...args: any[]): boolean;
  removeListener(event: string, listener: (...args: any[]) => void): void;
  removeAllListeners(event?: string): void;
}

// Utility Types
export type LogLevel = 'error' | 'warn' | 'info' | 'debug';
export type TimeFrame = '1m' | '5m' | '15m' | '30m' | '1h' | '4h' | '1d' | '1w';
export type OrderSide = 'buy' | 'sell';
export type OrderType = 'market' | 'limit' | 'stop' | 'stop-limit';
export type OrderStatus = 'open' | 'closed' | 'canceled' | 'expired' | 'rejected';
