import dotenv from 'dotenv';

// Load test environment variables
dotenv.config({ path: '.env.test' });

// Set test timeout
jest.setTimeout(30000);

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Global test utilities
global.beforeEach(() => {
  jest.clearAllMocks();
});

// Mock CCXT for testing
jest.mock('ccxt', () => ({
  binance: jest.fn().mockImplementation(() => ({
    apiKey: 'test-key',
    secret: 'test-secret',
    sandbox: true,
    loadMarkets: jest.fn().mockResolvedValue({}),
    fetchTicker: jest.fn().mockResolvedValue({
      symbol: 'BTC/USDT',
      last: 50000,
      bid: 49999,
      ask: 50001,
    }),
    createOrder: jest.fn().mockResolvedValue({
      id: 'test-order-id',
      symbol: 'BTC/USDT',
      type: 'limit',
      side: 'buy',
      amount: 0.001,
      price: 50000,
      status: 'open',
    }),
    fetchOrder: jest.fn().mockResolvedValue({
      id: 'test-order-id',
      status: 'closed',
      filled: 0.001,
    }),
    cancelOrder: jest.fn().mockResolvedValue({
      id: 'test-order-id',
      status: 'canceled',
    }),
  })),
  pro: {
    binance: jest.fn().mockImplementation(() => ({
      watchTicker: jest.fn(),
      watchOrderBook: jest.fn(),
      watchTrades: jest.fn(),
      watchOHLCV: jest.fn(),
    })),
  },
}));
