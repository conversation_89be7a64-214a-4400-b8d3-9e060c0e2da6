import * as fs from "fs";
import * as path from "path";
import * as crypto from "crypto-js";
import Joi from "joi";
import dotenv from "dotenv";
import { TradingConfig, ExchangeConfig, StrategyConfig } from "../types";
import { Logger } from "winston";

/**
 * Configuration validation schemas
 */
const exchangeConfigSchema = Joi.object({
  apiKey: Joi.string().required(),
  secret: Joi.string().required(),
  sandbox: Joi.boolean().default(true),
  testnet: Joi.boolean().default(true),
  rateLimit: Joi.number().min(1).max(2000).default(1200),
  timeout: Joi.number().min(1000).max(60000).default(30000),
  enableRateLimit: Joi.boolean().default(true),
});

const strategyConfigSchema = Joi.object({
  name: Joi.string().required(),
  symbol: Joi.string().required(),
  timeframe: Joi.string()
    .valid("1m", "5m", "15m", "30m", "1h", "4h", "1d", "1w")
    .required(),
  parameters: Joi.object().required(),
  enabled: Joi.boolean().default(true),
  riskPerTrade: Joi.number().min(0.001).max(0.1).default(0.02),
  maxPositionSize: Joi.number().min(1).required(),
});

const tradingConfigSchema = Joi.object({
  exchange: exchangeConfigSchema.required(),
  trading: Joi.object({
    maxPositionSize: Joi.number().min(1).required(),
    maxDrawdown: Joi.number().min(0.01).max(0.5).required(),
    riskPerTrade: Joi.number().min(0.001).max(0.1).required(),
    maxPortfolioExposure: Joi.number().min(1).required(),
    enablePaperTrading: Joi.boolean().default(true),
  }).required(),
  strategies: Joi.array().items(strategyConfigSchema).min(1).required(),
  logging: Joi.object({
    level: Joi.string().valid("error", "warn", "info", "debug").default("info"),
    filePath: Joi.string().default("./logs/trading-bot.log"),
    maxSize: Joi.string().default("10m"),
    maxFiles: Joi.number().min(1).max(10).default(5),
  }).default(),
});

/**
 * Secure configuration manager with encryption and validation
 */
export class ConfigManager {
  private config: TradingConfig | null = null;
  private encryptionKey: string;
  private logger: Logger;
  private configPath: string;
  private envPath: string;

  constructor(
    logger: Logger,
    configPath: string = "./config/trading-config.json"
  ) {
    this.logger = logger.child({ component: "ConfigManager" });
    this.configPath = path.resolve(configPath);
    this.envPath = path.resolve(".env");

    // Initialize encryption key from environment or generate one
    this.encryptionKey =
      process.env["ENCRYPTION_KEY"] || this.generateEncryptionKey();

    if (!process.env["ENCRYPTION_KEY"]) {
      this.logger.warn(
        "No ENCRYPTION_KEY found in environment. Generated a new one."
      );
      this.logger.warn(
        "Please set ENCRYPTION_KEY in your .env file for production use."
      );
    }
  }

  /**
   * Load and validate configuration
   */
  public async loadConfig(): Promise<TradingConfig> {
    try {
      // Load environment variables
      this.loadEnvironmentVariables();

      // Load configuration file
      const configData = await this.loadConfigFile();

      // Merge with environment overrides
      const mergedConfig = this.mergeWithEnvironment(configData);

      // Validate configuration
      const validatedConfig = await this.validateConfig(mergedConfig);

      // Decrypt sensitive data
      this.config = this.decryptSensitiveData(validatedConfig);

      this.logger.info("Configuration loaded and validated successfully");
      return this.config;
    } catch (error) {
      this.logger.error("Failed to load configuration", {
        error: (error as Error).message,
        stack: (error as Error).stack,
      });
      throw error;
    }
  }

  /**
   * Save configuration with encryption
   */
  public async saveConfig(config: TradingConfig): Promise<void> {
    try {
      // Validate configuration
      await this.validateConfig(config);

      // Encrypt sensitive data
      const encryptedConfig = this.encryptSensitiveData(config);

      // Save to file
      await this.saveConfigFile(encryptedConfig);

      this.config = config;
      this.logger.info("Configuration saved successfully");
    } catch (error) {
      this.logger.error("Failed to save configuration", {
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * Get current configuration
   */
  public getConfig(): TradingConfig {
    if (!this.config) {
      throw new Error("Configuration not loaded. Call loadConfig() first.");
    }
    return this.config;
  }

  /**
   * Get exchange configuration
   */
  public getExchangeConfig(): ExchangeConfig {
    return this.getConfig().exchange;
  }

  /**
   * Get strategy configurations
   */
  public getStrategyConfigs(): StrategyConfig[] {
    return this.getConfig().strategies;
  }

  /**
   * Get enabled strategy configurations
   */
  public getEnabledStrategies(): StrategyConfig[] {
    return this.getStrategyConfigs().filter((strategy) => strategy.enabled);
  }

  /**
   * Update strategy configuration
   */

  // Giải pháp 1: Type Assertion (nhanh nhất)
  public updateStrategyConfig(
    strategyName: string,
    updates: Partial<StrategyConfig>
  ): void {
    if (!this.config) {
      throw new Error("Configuration not loaded");
    }

    const strategyIndex = this.config.strategies.findIndex(
      (s) => s.name === strategyName
    );
    if (strategyIndex === -1) {
      throw new Error(`Strategy ${strategyName} not found`);
    }

    // Type assertion - đảm bảo TypeScript hiểu kết quả là StrategyConfig hợp lệ
    this.config.strategies[strategyIndex] = {
      ...this.config.strategies[strategyIndex],
      ...updates,
    } as StrategyConfig;

    this.logger.info(`Strategy configuration updated: ${strategyName}`);
  }

  /**
   * Add new strategy configuration
   */
  public addStrategyConfig(strategy: StrategyConfig): void {
    if (!this.config) {
      throw new Error("Configuration not loaded");
    }

    // Check if strategy already exists
    if (this.config.strategies.some((s) => s.name === strategy.name)) {
      throw new Error(`Strategy ${strategy.name} already exists`);
    }

    this.config.strategies.push(strategy);
    this.logger.info(`Strategy configuration added: ${strategy.name}`);
  }

  /**
   * Remove strategy configuration
   */
  public removeStrategyConfig(strategyName: string): void {
    if (!this.config) {
      throw new Error("Configuration not loaded");
    }

    const initialLength = this.config.strategies.length;
    this.config.strategies = this.config.strategies.filter(
      (s) => s.name !== strategyName
    );

    if (this.config.strategies.length === initialLength) {
      throw new Error(`Strategy ${strategyName} not found`);
    }

    this.logger.info(`Strategy configuration removed: ${strategyName}`);
  }

  /**
   * Load environment variables
   */
  private loadEnvironmentVariables(): void {
    if (fs.existsSync(this.envPath)) {
      dotenv.config({ path: this.envPath });
      this.logger.debug("Environment variables loaded from .env file");
    } else {
      this.logger.warn(
        ".env file not found, using system environment variables"
      );
    }
  }

  /**
   * Load configuration file
   */
  private async loadConfigFile(): Promise<any> {
    if (!fs.existsSync(this.configPath)) {
      throw new Error(`Configuration file not found: ${this.configPath}`);
    }

    const configContent = fs.readFileSync(this.configPath, "utf8");
    return JSON.parse(configContent);
  }

  /**
   * Save configuration file
   */
  private async saveConfigFile(config: any): Promise<void> {
    const configDir = path.dirname(this.configPath);
    if (!fs.existsSync(configDir)) {
      fs.mkdirSync(configDir, { recursive: true });
    }

    const configContent = JSON.stringify(config, null, 2);
    fs.writeFileSync(this.configPath, configContent, "utf8");
  }

  /**
   * Merge configuration with environment variables
   */
  private mergeWithEnvironment(config: any): any {
    const envOverrides = {
      exchange: {
        ...config.exchange,
        apiKey: process.env["BINANCE_API_KEY"] || config.exchange?.apiKey,
        secret: process.env["BINANCE_SECRET"] || config.exchange?.secret,
        sandbox:
          process.env["BINANCE_SANDBOX"] === "true" || config.exchange?.sandbox,
        testnet:
          process.env["BINANCE_TESTNET"] === "true" || config.exchange?.testnet,
        rateLimit:
          parseInt(process.env["BINANCE_RATE_LIMIT"] || "0") ||
          config.exchange?.rateLimit,
      },
      trading: {
        ...config.trading,
        maxPositionSize:
          parseFloat(process.env["MAX_POSITION_SIZE"] || "0") ||
          config.trading?.maxPositionSize,
        maxDrawdown:
          parseFloat(process.env["MAX_DRAWDOWN"] || "0") ||
          config.trading?.maxDrawdown,
        riskPerTrade:
          parseFloat(process.env["RISK_PER_TRADE"] || "0") ||
          config.trading?.riskPerTrade,
        maxPortfolioExposure:
          parseFloat(process.env["MAX_PORTFOLIO_EXPOSURE"] || "0") ||
          config.trading?.maxPortfolioExposure,
        enablePaperTrading:
          process.env["ENABLE_PAPER_TRADING"] === "true" ||
          config.trading?.enablePaperTrading,
      },
      logging: {
        ...config.logging,
        level: process.env["LOG_LEVEL"] || config.logging?.level,
        filePath: process.env["LOG_FILE_PATH"] || config.logging?.filePath,
      },
    };

    return {
      ...config,
      ...envOverrides,
    };
  }

  /**
   * Validate configuration against schema
   */
  private async validateConfig(config: any): Promise<TradingConfig> {
    const { error, value } = tradingConfigSchema.validate(config, {
      abortEarly: false,
      allowUnknown: false,
      stripUnknown: true,
    });

    if (error) {
      const errorMessage = error.details
        .map((detail) => detail.message)
        .join(", ");
      throw new Error(`Configuration validation failed: ${errorMessage}`);
    }

    return value as TradingConfig;
  }

  /**
   * Encrypt sensitive configuration data
   */
  private encryptSensitiveData(config: TradingConfig): any {
    const configCopy = JSON.parse(JSON.stringify(config));

    // Encrypt API credentials
    if (configCopy.exchange.apiKey) {
      configCopy.exchange.apiKey = this.encrypt(configCopy.exchange.apiKey);
    }
    if (configCopy.exchange.secret) {
      configCopy.exchange.secret = this.encrypt(configCopy.exchange.secret);
    }

    return configCopy;
  }

  /**
   * Decrypt sensitive configuration data
   */
  private decryptSensitiveData(config: any): TradingConfig {
    const configCopy = JSON.parse(JSON.stringify(config));

    // Decrypt API credentials
    if (
      configCopy.exchange.apiKey &&
      this.isEncrypted(configCopy.exchange.apiKey)
    ) {
      configCopy.exchange.apiKey = this.decrypt(configCopy.exchange.apiKey);
    }
    if (
      configCopy.exchange.secret &&
      this.isEncrypted(configCopy.exchange.secret)
    ) {
      configCopy.exchange.secret = this.decrypt(configCopy.exchange.secret);
    }

    return configCopy as TradingConfig;
  }

  /**
   * Encrypt a string
   */
  private encrypt(text: string): string {
    return crypto.AES.encrypt(text, this.encryptionKey).toString();
  }

  /**
   * Decrypt a string
   */
  private decrypt(encryptedText: string): string {
    const bytes = crypto.AES.decrypt(encryptedText, this.encryptionKey);
    return bytes.toString(crypto.enc.Utf8);
  }

  /**
   * Check if a string is encrypted
   */
  private isEncrypted(text: string): boolean {
    try {
      crypto.AES.decrypt(text, this.encryptionKey);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Generate a random encryption key
   */
  private generateEncryptionKey(): string {
    return crypto.lib.WordArray.random(256 / 8).toString();
  }

  /**
   * Validate API credentials
   */
  public async validateCredentials(): Promise<boolean> {
    try {
      const config = this.getConfig();

      // Basic validation
      if (!config.exchange.apiKey || !config.exchange.secret) {
        return false;
      }

      // Additional validation can be added here
      // For example, test connection to exchange

      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get configuration summary (without sensitive data)
   */
  public getConfigSummary(): Record<string, any> {
    if (!this.config) {
      return { status: "not_loaded" };
    }

    return {
      exchange: {
        sandbox: this.config.exchange.sandbox,
        testnet: this.config.exchange.testnet,
        rateLimit: this.config.exchange.rateLimit,
        hasCredentials: !!(
          this.config.exchange.apiKey && this.config.exchange.secret
        ),
      },
      trading: {
        maxPositionSize: this.config.trading.maxPositionSize,
        maxDrawdown: this.config.trading.maxDrawdown,
        riskPerTrade: this.config.trading.riskPerTrade,
        maxPortfolioExposure: this.config.trading.maxPortfolioExposure,
        enablePaperTrading: this.config.trading.enablePaperTrading,
      },
      strategies: this.config.strategies.map((strategy) => ({
        name: strategy.name,
        symbol: strategy.symbol,
        timeframe: strategy.timeframe,
        enabled: strategy.enabled,
      })),
      logging: this.config.logging,
    };
  }
}
