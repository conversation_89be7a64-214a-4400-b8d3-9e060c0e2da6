# CCXT Trading Bot

A comprehensive Node.js cryptocurrency trading bot architecture using the CCXT library (version 4.4.99+) specifically designed for Binance trading. This bot features a modular architecture with real-time market data processing, advanced order management, risk controls, and multiple trading strategies.

## 🏗️ Architecture Overview

The trading bot is built with a modular, event-driven architecture that ensures scalability, maintainability, and fault tolerance:

### Core Modules

1. **API Connectivity Module** - Handles REST and WebSocket connections with rate limiting
2. **Market Data Module** - Real-time price feeds and data caching
3. **Order Management Module** - Order execution and position tracking
4. **Strategy Engine** - Pluggable trading strategies
5. **Risk Management Module** - Position sizing and risk controls
6. **Logging & Monitoring** - Comprehensive logging and performance tracking
7. **Error Handling Module** - Graceful error recovery

## 🚀 Features

### Trading Features
- ✅ **Real-time Market Data** - Live price feeds, order books, and trade data
- ✅ **Multiple Order Types** - Market, limit, stop, and stop-limit orders
- ✅ **Position Management** - Automatic position tracking and PnL calculation
- ✅ **Paper Trading** - Risk-free strategy testing
- ✅ **Multiple Strategies** - Moving averages, RSI, Bollinger Bands, and custom strategies
- ✅ **Risk Management** - Stop-loss, take-profit, and exposure limits

### Technical Features
- ✅ **TypeScript** - Full type safety with CCXT type definitions
- ✅ **Event-Driven Architecture** - Decoupled modules with EventEmitter
- ✅ **Rate Limiting** - Respects Binance's 1200 requests/minute limit
- ✅ **Connection Management** - Automatic reconnection and connection pooling
- ✅ **Encrypted Configuration** - Secure credential storage
- ✅ **Comprehensive Logging** - Structured JSON logging with Winston
- ✅ **Error Recovery** - Graceful handling of network and API errors

## 📋 Prerequisites

- Node.js >= 16.0.0
- npm >= 8.0.0
- Binance API credentials (for live trading)
- TypeScript knowledge (recommended)

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ccxt-bot
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Set up Binance API credentials**
   - Create API keys in your Binance account
   - Enable spot trading permissions
   - For testing, use Binance Testnet credentials

## ⚙️ Configuration

### Environment Variables (.env)

```bash
# Binance API Configuration
BINANCE_API_KEY=your_api_key_here
BINANCE_SECRET=your_secret_here
BINANCE_TESTNET=true
BINANCE_SANDBOX=true

# Trading Configuration
MAX_POSITION_SIZE=1000
MAX_DRAWDOWN=0.15
RISK_PER_TRADE=0.02
MAX_PORTFOLIO_EXPOSURE=5000
ENABLE_PAPER_TRADING=true

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_PATH=./logs/trading-bot.log

# Security
ENCRYPTION_KEY=your_32_character_encryption_key_here
```

### Trading Configuration (config/trading-config.json)

```json
{
  "exchange": {
    "apiKey": "your_api_key_here",
    "secret": "your_secret_here",
    "sandbox": true,
    "testnet": true,
    "rateLimit": 1200
  },
  "trading": {
    "maxPositionSize": 1000,
    "maxDrawdown": 0.15,
    "riskPerTrade": 0.02,
    "maxPortfolioExposure": 5000,
    "enablePaperTrading": true
  },
  "strategies": [
    {
      "name": "MovingAverage",
      "symbol": "BTC/USDT",
      "timeframe": "1h",
      "parameters": {
        "fastPeriod": 12,
        "slowPeriod": 26
      },
      "enabled": true
    }
  ]
}
```

## 🚀 Usage

### Development Mode

```bash
# Start in development mode with hot reload
npm run dev

# Start with file watching
npm run dev:watch
```

### Production Mode

```bash
# Build the project
npm run build

# Start in production mode
npm start
```

### Testing

```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch
```

## 📊 Trading Strategies

### Built-in Strategies

1. **Moving Average Crossover**
   - Fast and slow moving average crossover signals
   - Configurable periods and timeframes

2. **RSI Strategy**
   - Relative Strength Index overbought/oversold signals
   - Configurable RSI period and thresholds

3. **Bollinger Bands**
   - Volatility-based trading signals
   - Configurable period and standard deviation

### Custom Strategy Development

```typescript
import { BaseStrategy } from './strategies/BaseStrategy';
import { StrategySignal, CandleData } from '../types';

export class CustomStrategy extends BaseStrategy {
  public async generateSignal(candles: CandleData[]): Promise<StrategySignal | null> {
    // Implement your trading logic here
    const signal: StrategySignal = {
      symbol: this.config.symbol,
      action: 'buy', // 'buy', 'sell', or 'hold'
      confidence: 0.8,
      price: candles[candles.length - 1].close,
      quantity: this.calculatePositionSize(),
      timestamp: Date.now()
    };
    
    return signal;
  }
}
```

## 🛡️ Risk Management

The bot includes comprehensive risk management features:

- **Position Sizing** - Automatic calculation based on risk per trade
- **Stop Loss** - Automatic stop-loss orders to limit losses
- **Take Profit** - Automatic profit-taking at target levels
- **Maximum Drawdown** - Portfolio-level drawdown protection
- **Exposure Limits** - Maximum portfolio exposure controls
- **Paper Trading** - Risk-free testing mode

## 📈 Performance Analytics

Track your trading performance with built-in analytics:

- **Sharpe Ratio** - Risk-adjusted returns
- **Maximum Drawdown** - Largest peak-to-trough decline
- **Win Rate** - Percentage of profitable trades
- **Profit Factor** - Ratio of gross profit to gross loss
- **Trade Statistics** - Detailed trade analysis

## 🔧 API Reference

### Core Classes

#### TradingBot
Main application class that orchestrates all modules.

```typescript
const bot = new TradingBot();
await bot.initialize();
await bot.start();
```

#### ExchangeConnector
Handles connection to Binance exchange with rate limiting.

```typescript
const connector = new ExchangeConnector(eventBus, logger, config);
const exchange = connector.getExchange(); // REST API
const proExchange = connector.getProExchange(); // WebSocket API
```

#### MarketDataManager
Manages real-time market data subscriptions.

```typescript
const marketData = new MarketDataManager(eventBus, logger, connector);
await marketData.subscribeTicker('BTC/USDT');
await marketData.subscribeOHLCV('BTC/USDT', '1h');
```

#### OrderManager
Handles order execution and position tracking.

```typescript
const orderManager = new OrderManager(eventBus, logger, connector);
const order = await orderManager.createOrder({
  symbol: 'BTC/USDT',
  type: 'limit',
  side: 'buy',
  amount: 0.001,
  price: 50000
});
```

## 🔍 Monitoring and Debugging

### Logging

The bot uses Winston for structured logging:

```typescript
// Log levels: error, warn, info, debug
logger.info('Order executed', {
  orderId: order.id,
  symbol: order.symbol,
  price: order.price
});
```

### Event System

Monitor bot activity through the event system:

```typescript
eventBus.on('order_filled', (event) => {
  console.log('Order filled:', event.data);
});

eventBus.on('signal_generated', (event) => {
  console.log('Trading signal:', event.data);
});
```

### Health Checks

```typescript
// Check module health
const health = await moduleManager.healthCheckAll();
console.log('Module health:', health);

// Get system status
const status = bot.getStatus();
console.log('Bot status:', status);
```

## 🧪 Testing

The project includes comprehensive test coverage:

- **Unit Tests** - Individual module testing
- **Integration Tests** - Module interaction testing
- **Mock Exchange** - Simulated exchange for testing
- **Paper Trading** - Live testing without real funds

## 🔒 Security

- **Encrypted Credentials** - API keys stored with AES encryption
- **Environment Variables** - Sensitive data in environment files
- **Input Validation** - Joi schema validation for all inputs
- **Rate Limiting** - Prevents API abuse and account restrictions
- **Error Handling** - Secure error messages without credential exposure

## 📝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## ⚠️ Disclaimer

This trading bot is for educational and research purposes. Cryptocurrency trading involves substantial risk of loss. Always test strategies thoroughly in paper trading mode before using real funds. The authors are not responsible for any financial losses incurred through the use of this software.

## 📄 License

MIT License - see LICENSE file for details.

## 🤝 Support

For questions, issues, or contributions:
- Create an issue on GitHub
- Check the documentation
- Review the test files for usage examples
