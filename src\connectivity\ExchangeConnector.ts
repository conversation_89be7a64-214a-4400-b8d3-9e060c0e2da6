import ccxt from "ccxt";
import * as ccxtPro from "ccxt";
import { BaseModule } from "../core/BaseModule";
import { EventBus } from "../core/EventBus";
import { ExchangeConfig, TradingError } from "../types";
import { Logger } from "winston";

/**
 * Rate limiter for API requests
 */
class RateLimiter {
  private requests: number[] = [];
  private maxRequests: number;
  private windowMs: number;

  constructor(maxRequests: number = 1200, windowMs: number = 60000) {
    this.maxRequests = maxRequests;
    this.windowMs = windowMs;
  }

  /**
   * Check if request is allowed
   */
  public canMakeRequest(): boolean {
    const now = Date.now();

    // Remove old requests outside the window
    this.requests = this.requests.filter(
      (timestamp) => now - timestamp < this.windowMs
    );

    return this.requests.length < this.maxRequests;
  }

  /**
   * Record a request
   */
  public recordRequest(): void {
    this.requests.push(Date.now());
  }

  /**
   * Get current request count
   */
  public getCurrentCount(): number {
    const now = Date.now();
    this.requests = this.requests.filter(
      (timestamp) => now - timestamp < this.windowMs
    );
    return this.requests.length;
  }

  /**
   * Get time until next request is allowed
   */
  public getTimeUntilNextRequest(): number {
    if (this.canMakeRequest()) {
      return 0;
    }

    const now = Date.now();
    const oldestRequest = Math.min(...this.requests);
    return Math.max(0, this.windowMs - (now - oldestRequest));
  }
}

/**
 * Connection pool for managing multiple exchange instances
 */
class ConnectionPool {
  private connections: Map<string, any> = new Map();
  private maxConnections: number = 5;
  private logger: Logger;

  constructor(logger: Logger, maxConnections: number = 5) {
    this.logger = logger;
    this.maxConnections = maxConnections;
  }

  /**
   * Get or create a connection
   */
  public getConnection(key: string, factory: () => any): any {
    if (this.connections.has(key)) {
      return this.connections.get(key)!;
    }

    if (this.connections.size >= this.maxConnections) {
      // Remove oldest connection
      const firstKey = this.connections.keys().next().value;
      this.connections.delete(firstKey as string);
      this.logger.debug(`Removed connection from pool: ${firstKey}`);
    }

    const connection = factory();
    this.connections.set(key, connection);
    this.logger.debug(`Added connection to pool: ${key}`);

    return connection;
  }

  /**
   * Remove a connection
   */
  public removeConnection(key: string): void {
    this.connections.delete(key);
    this.logger.debug(`Removed connection from pool: ${key}`);
  }

  /**
   * Clear all connections
   */
  public clear(): void {
    this.connections.clear();
    this.logger.debug("Cleared all connections from pool");
  }

  /**
   * Get pool status
   */
  public getStatus(): Record<string, any> {
    return {
      activeConnections: this.connections.size,
      maxConnections: this.maxConnections,
      connectionKeys: Array.from(this.connections.keys()),
    };
  }
}

/**
 * Exchange connector with rate limiting and connection management
 */
export class ExchangeConnector extends BaseModule {
  private exchange: any;
  private proExchange: any;
  private config: ExchangeConfig;
  private rateLimiter: RateLimiter;
  private connectionPool: ConnectionPool;
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = 10;
  private reconnectDelay: number = 5000;
  private isConnected: boolean = false;

  constructor(eventBus: EventBus, logger: Logger, config: ExchangeConfig) {
    super("ExchangeConnector", eventBus, logger);
    this.config = config;
    this.rateLimiter = new RateLimiter(config.rateLimit, 60000);
    this.connectionPool = new ConnectionPool(logger);
  }

  /**
   * Initialize the exchange connector
   */
  protected async onInitialize(): Promise<void> {
    try {
      // Initialize REST API connection
      this.exchange = new ccxt.binance({
        apiKey: this.config.apiKey,
        secret: this.config.secret,
        sandbox: this.config.sandbox,
        enableRateLimit: this.config.enableRateLimit,
        timeout: this.config.timeout,
        options: {
          defaultType: "spot", // Use spot trading
          adjustForTimeDifference: true,
        },
      });

      // Initialize WebSocket connection
      this.proExchange = new ccxtPro.binance({
        apiKey: this.config.apiKey,
        secret: this.config.secret,
        sandbox: this.config.sandbox,
        options: {
          defaultType: "spot",
        },
      });

      // Load markets
      await this.loadMarkets();

      this.logger.info("Exchange connector initialized successfully");
    } catch (error) {
      this.logger.error("Failed to initialize exchange connector", {
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * Start the exchange connector
   */
  protected async onStart(): Promise<void> {
    try {
      // Test connection
      await this.testConnection();
      this.isConnected = true;
      this.reconnectAttempts = 0;

      this.logger.info("Exchange connector started successfully");
    } catch (error) {
      this.logger.error("Failed to start exchange connector", {
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * Stop the exchange connector
   */
  protected async onStop(): Promise<void> {
    try {
      this.isConnected = false;

      // Close WebSocket connections
      if (this.proExchange) {
        await this.proExchange.close();
      }

      // Clear connection pool
      this.connectionPool.clear();

      this.logger.info("Exchange connector stopped successfully");
    } catch (error) {
      this.logger.error("Failed to stop exchange connector", {
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * Get module-specific status
   */
  protected getModuleSpecificStatus(): Record<string, any> {
    return {
      connected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts,
      rateLimiter: {
        currentRequests: this.rateLimiter.getCurrentCount(),
        maxRequests: this.rateLimiter["maxRequests"],
        timeUntilNextRequest: this.rateLimiter.getTimeUntilNextRequest(),
      },
      connectionPool: this.connectionPool.getStatus(),
      exchange: {
        id: this.exchange?.id,
        sandbox: this.config.sandbox,
        testnet: this.config.testnet,
        rateLimit: this.config.rateLimit,
      },
    };
  }

  /**
   * Get REST exchange instance
   */
  public getExchange(): any {
    if (!this.exchange) {
      throw new Error("Exchange not initialized");
    }
    return this.exchange;
  }

  /**
   * Get WebSocket exchange instance
   */
  public getProExchange(): ccxtPro.binance {
    if (!this.proExchange) {
      throw new Error("Pro exchange not initialized");
    }
    return this.proExchange;
  }

  /**
   * Execute API request with rate limiting
   */
  public async executeRequest<T>(
    operation: () => Promise<T>,
    retries: number = 3
  ): Promise<T> {
    // Check rate limit
    if (!this.rateLimiter.canMakeRequest()) {
      const waitTime = this.rateLimiter.getTimeUntilNextRequest();
      this.logger.debug(`Rate limit reached, waiting ${waitTime}ms`);
      await this.sleep(waitTime);
    }

    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        this.rateLimiter.recordRequest();
        const result = await operation();

        // Reset reconnect attempts on successful request
        this.reconnectAttempts = 0;

        return result;
      } catch (error) {
        lastError = error as Error;

        const tradingError = this.createTradingError(lastError);
        this.logger.warn(`API request failed (attempt ${attempt}/${retries})`, {
          error: tradingError.message,
          type: tradingError.type,
          retryable: tradingError.retryable,
        });

        // Handle specific error types
        if (tradingError.type === "rate_limit") {
          await this.sleep(this.rateLimiter.getTimeUntilNextRequest());
        } else if (tradingError.type === "network" && tradingError.retryable) {
          await this.sleep(1000 * attempt); // Exponential backoff
        } else if (!tradingError.retryable) {
          throw tradingError;
        }

        // If this was the last attempt, try to reconnect
        if (attempt === retries) {
          await this.handleConnectionLoss();
        }
      }
    }

    throw lastError;
  }

  /**
   * Load markets from exchange
   */
  private async loadMarkets(): Promise<void> {
    if (!this.exchange) {
      throw new Error("Exchange not initialized");
    }

    try {
      await this.exchange.loadMarkets();
      this.logger.info(
        `Loaded ${Object.keys(this.exchange.markets).length} markets`
      );
    } catch (error) {
      this.logger.error("Failed to load markets", {
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * Test connection to exchange
   */
  private async testConnection(): Promise<void> {
    if (!this.exchange) {
      throw new Error("Exchange not initialized");
    }

    try {
      // Test with a simple API call
      await this.exchange.fetchStatus();
      this.logger.debug("Connection test successful");
    } catch (error) {
      this.logger.error("Connection test failed", {
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * Handle connection loss and attempt reconnection
   */
  private async handleConnectionLoss(): Promise<void> {
    this.isConnected = false;
    this.emitSystemEvent("connection_lost", {
      reconnectAttempts: this.reconnectAttempts,
    });

    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      const error = new Error(
        `Max reconnection attempts (${this.maxReconnectAttempts}) exceeded`
      );
      this.handleError(error, "reconnection");
      throw error;
    }

    this.reconnectAttempts++;
    this.logger.info(
      `Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`
    );

    await this.sleep(this.reconnectDelay * this.reconnectAttempts);

    try {
      await this.testConnection();
      this.isConnected = true;
      this.reconnectAttempts = 0;

      this.emitSystemEvent("connection_restored", {
        reconnectAttempts: this.reconnectAttempts,
      });

      this.logger.info("Connection restored successfully");
    } catch (error) {
      this.logger.error("Reconnection failed", {
        error: (error as Error).message,
        attempt: this.reconnectAttempts,
      });
      throw error;
    }
  }

  /**
   * Create a standardized trading error
   */
  private createTradingError(error: Error): TradingError {
    const tradingError = error as TradingError;

    // Set default properties if not already set
    tradingError.code = tradingError.code || "UNKNOWN_ERROR";
    tradingError.timestamp = tradingError.timestamp || Date.now();
    tradingError.retryable =
      tradingError.retryable !== undefined ? tradingError.retryable : true;

    // Determine error type based on message
    if (error.message.includes("rate limit") || error.message.includes("429")) {
      tradingError.type = "rate_limit";
      tradingError.retryable = true;
    } else if (
      error.message.includes("network") ||
      error.message.includes("timeout")
    ) {
      tradingError.type = "network";
      tradingError.retryable = true;
    } else if (
      error.message.includes("insufficient") ||
      error.message.includes("balance")
    ) {
      tradingError.type = "insufficient_balance";
      tradingError.retryable = false;
    } else if (error.message.includes("maintenance")) {
      tradingError.type = "exchange_maintenance";
      tradingError.retryable = true;
    } else if (
      error.message.includes("invalid") ||
      error.message.includes("validation")
    ) {
      tradingError.type = "validation";
      tradingError.retryable = false;
    } else {
      tradingError.type = "api";
    }

    return tradingError;
  }

  /**
   * Sleep utility
   */
  private sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Health check implementation
   */
  protected override async performHealthCheck(): Promise<boolean> {
    try {
      if (!this.isConnected || !this.exchange) {
        return false;
      }

      // Test with a lightweight API call
      await this.exchange.fetchStatus();
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get rate limiter status
   */
  public getRateLimiterStatus(): Record<string, any> {
    return {
      currentRequests: this.rateLimiter.getCurrentCount(),
      maxRequests: this.rateLimiter["maxRequests"],
      timeUntilNextRequest: this.rateLimiter.getTimeUntilNextRequest(),
      canMakeRequest: this.rateLimiter.canMakeRequest(),
    };
  }

  /**
   * Force reconnection
   */
  public async forceReconnect(): Promise<void> {
    this.logger.info("Forcing reconnection");
    this.isConnected = false;
    await this.handleConnectionLoss();
  }
}
