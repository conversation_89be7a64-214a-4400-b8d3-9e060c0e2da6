import { EventEmitter } from "events";
import { TradingEvent, IEventEmitter } from "../types";
import { Logger } from "winston";

/**
 * Central event bus for the trading bot
 * Handles all inter-module communication through events
 */

export type ModuleEvent =
  | "module_started"
  | "module_stopped"
  | "module_error"
  | "connection_lost"
  | "connection_restored";
export class EventBus extends EventEmitter implements IEventEmitter {
  private logger: Logger;
  private eventHistory: TradingEvent[] = [];
  private maxHistorySize: number = 1000;

  constructor(logger: Logger) {
    super();
    this.logger = logger;
    this.setMaxListeners(50); // Increase max listeners for multiple strategies
    this.setupEventLogging();
  }

  /**
   * Emit a trading event with automatic logging and history tracking
   */
  public emitTradingEvent(event: TradingEvent): boolean {
    this.addToHistory(event);
    this.logger.debug("Event emitted", {
      type: event.type,
      source: event.source,
      timestamp: event.timestamp,
      data: event.data,
    });

    return this.emit(event.type, event);
  }

  /**
   * Subscribe to trading events with type safety
   */
  public onTradingEvent<T extends TradingEvent>(
    eventType: string,
    listener: (event: T) => void
  ): void {
    this.on(eventType, listener);
  }

  /**
   * Get recent event history
   */
  public getEventHistory(
    eventType?: string,
    limit: number = 100
  ): TradingEvent[] {
    let events = this.eventHistory;

    if (eventType) {
      events = events.filter((event) => event.type === eventType);
    }

    return events.slice(-limit);
  }

  /**
   * Clear event history
   */
  public clearHistory(): void {
    this.eventHistory = [];
    this.logger.info("Event history cleared");
  }

  /**
   * Get event statistics
   */
  public getEventStats(): Record<string, number> {
    const stats: Record<string, number> = {};

    this.eventHistory.forEach((event) => {
      stats[event.type] = (stats[event.type] || 0) + 1;
    });

    return stats;
  }

  /**
   * Setup automatic event logging
   */
  private setupEventLogging(): void {
    // Log all events for debugging
    this.onAny((eventType: string, ...args: any[]) => {
      this.logger.debug("Event received", {
        eventType,
        argsCount: args.length,
        timestamp: Date.now(),
      });
    });

    // Log errors
    this.on("error", (error: Error) => {
      this.logger.error("EventBus error", {
        message: error.message,
        stack: error.stack,
        timestamp: Date.now(),
      });
    });
  }

  /**
   * Add event to history with size management
   */
  private addToHistory(event: TradingEvent): void {
    this.eventHistory.push(event);

    // Maintain history size limit
    if (this.eventHistory.length > this.maxHistorySize) {
      this.eventHistory = this.eventHistory.slice(-this.maxHistorySize);
    }
  }

  /**
   * Listen to any event (for debugging)
   */
  private onAny(listener: (eventType: string, ...args: any[]) => void): void {
    const originalEmit = this.emit;
    this.emit = function (eventType: string | symbol, ...args: any[]): boolean {
      if (typeof eventType === "string") {
        listener(eventType, ...args);
      }
      return originalEmit.call(this, eventType, ...args);
    };
  }
}

/**
 * Event factory for creating standardized trading events
 */
export class EventFactory {
  /**
   * Create a market data event
   */
  static createMarketDataEvent(
    type: "ticker" | "orderbook" | "trade" | "ohlcv",
    symbol: string,
    data: any,
    source: string = "market-data"
  ): TradingEvent {
    return {
      type,
      timestamp: Date.now(),
      data: { symbol, ...data },
      source,
    };
  }

  /**
   * Create an order event
   */
  static createOrderEvent(
    type: "order_created" | "order_filled" | "order_canceled" | "order_failed",
    orderId: string,
    data: any,
    source: string = "order-manager"
  ): TradingEvent {
    return {
      type,
      timestamp: Date.now(),
      data: { orderId, ...data },
      source,
    };
  }

  /**
   * Create a strategy event
   */
  static createStrategyEvent(
    type:
      | "signal_generated"
      | "strategy_started"
      | "strategy_stopped"
      | "strategy_error",
    strategyName: string,
    data: any,
    source: string = "strategy-engine"
  ): TradingEvent {
    return {
      type,
      timestamp: Date.now(),
      data: { strategyName, ...data },
      source,
    };
  }

  /**
   * Create a risk management event
   */
  static createRiskEvent(
    type: "risk_limit_exceeded" | "position_closed" | "stop_loss_triggered",
    data: any,
    source: string = "risk-manager"
  ): TradingEvent {
    return {
      type,
      timestamp: Date.now(),
      data,
      source,
    };
  }

  /**
   * Create a system event
   */
  static createSystemEvent(
    type: ModuleEvent,
    data: any,
    source: string = "system"
  ): TradingEvent {
    return {
      type,
      timestamp: Date.now(),
      data,
      source,
    };
  }
}

/**
 * Event constants for type safety
 */
export const EventTypes = {
  // Market Data Events
  TICKER_UPDATE: "ticker_update",
  ORDERBOOK_UPDATE: "orderbook_update",
  TRADE_UPDATE: "trade_update",
  OHLCV_UPDATE: "ohlcv_update",

  // Order Events
  ORDER_CREATED: "order_created",
  ORDER_FILLED: "order_filled",
  ORDER_CANCELED: "order_canceled",
  ORDER_FAILED: "order_failed",

  // Strategy Events
  SIGNAL_GENERATED: "signal_generated",
  STRATEGY_STARTED: "strategy_started",
  STRATEGY_STOPPED: "strategy_stopped",
  STRATEGY_ERROR: "strategy_error",

  // Risk Management Events
  RISK_LIMIT_EXCEEDED: "risk_limit_exceeded",
  POSITION_CLOSED: "position_closed",
  STOP_LOSS_TRIGGERED: "stop_loss_triggered",
  TAKE_PROFIT_TRIGGERED: "take_profit_triggered",

  // System Events
  MODULE_STARTED: "module_started",
  MODULE_STOPPED: "module_stopped",
  MODULE_ERROR: "module_error",
  CONNECTION_LOST: "connection_lost",
  CONNECTION_RESTORED: "connection_restored",

  // Performance Events
  TRADE_COMPLETED: "trade_completed",
  PERFORMANCE_UPDATE: "performance_update",
  DRAWDOWN_ALERT: "drawdown_alert",
} as const;

export type EventType = (typeof EventTypes)[keyof typeof EventTypes];
