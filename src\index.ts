import dotenv from "dotenv";
import winston from "winston";
import { EventBus } from "./core/EventBus";
import { ModuleManager } from "./core/BaseModule";
import { ConfigManager } from "./config/ConfigManager";
import { ExchangeConnector } from "./connectivity/ExchangeConnector";
import { MarketDataManager } from "./market-data/MarketDataManager";
import { OrderManager } from "./order-management/OrderManager";

// Load environment variables
dotenv.config();

/**
 * Main trading bot application
 */
class TradingBot {
  private logger: winston.Logger;
  private eventBus: EventBus;
  private moduleManager: ModuleManager;
  private configManager: ConfigManager;
  private exchangeConnector?: ExchangeConnector;
  private marketDataManager?: MarketDataManager;
  private orderManager?: OrderManager;
  private isRunning: boolean = false;

  constructor() {
    // Initialize logger
    this.logger = this.createLogger();

    // Initialize event bus
    this.eventBus = new EventBus(this.logger);

    // Initialize module manager
    this.moduleManager = new ModuleManager(this.eventBus, this.logger);

    // Initialize configuration manager
    this.configManager = new ConfigManager(this.logger);

    this.setupEventListeners();
    this.setupGracefulShutdown();
  }

  /**
   * Initialize the trading bot
   */
  public async initialize(): Promise<void> {
    try {
      this.logger.info("Initializing CCXT Trading Bot...");

      // Load configuration
      const config = await this.configManager.loadConfig();
      this.logger.info("Configuration loaded successfully");

      // Initialize exchange connector
      this.exchangeConnector = new ExchangeConnector(
        this.eventBus,
        this.logger,
        config.exchange
      );
      this.moduleManager.registerModule(this.exchangeConnector);

      // Initialize market data manager
      this.marketDataManager = new MarketDataManager(
        this.eventBus,
        this.logger,
        this.exchangeConnector
      );
      this.moduleManager.registerModule(this.marketDataManager);

      // Initialize order manager
      this.orderManager = new OrderManager(
        this.eventBus,
        this.logger,
        this.exchangeConnector,
        config.trading.enablePaperTrading
      );
      this.moduleManager.registerModule(this.orderManager);

      // Initialize all modules
      await this.moduleManager.initializeAll();

      this.logger.info("Trading bot initialized successfully");
    } catch (error) {
      this.logger.error("Failed to initialize trading bot", {
        error: (error as Error).message,
        stack: (error as Error).stack,
      });
      throw error;
    }
  }

  /**
   * Start the trading bot
   */
  public async start(): Promise<void> {
    try {
      if (this.isRunning) {
        this.logger.warn("Trading bot is already running");
        return;
      }

      this.logger.info("Starting CCXT Trading Bot...");

      // Start all modules
      await this.moduleManager.startAll();

      // Subscribe to market data for configured strategies
      await this.subscribeToMarketData();

      this.isRunning = true;
      this.logger.info("Trading bot started successfully");

      // Log system status
      this.logSystemStatus();
    } catch (error) {
      this.logger.error("Failed to start trading bot", {
        error: (error as Error).message,
        stack: (error as Error).stack,
      });
      throw error;
    }
  }

  /**
   * Stop the trading bot
   */
  public async stop(): Promise<void> {
    try {
      if (!this.isRunning) {
        this.logger.warn("Trading bot is not running");
        return;
      }

      this.logger.info("Stopping CCXT Trading Bot...");

      this.isRunning = false;

      // Stop all modules
      await this.moduleManager.stopAll();

      this.logger.info("Trading bot stopped successfully");
    } catch (error) {
      this.logger.error("Failed to stop trading bot", {
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * Get trading bot status
   */
  public getStatus(): Record<string, any> {
    return {
      running: this.isRunning,
      uptime: this.isRunning ? Date.now() - (this.startTime || 0) : 0,
      modules: this.moduleManager.getAllStatuses(),
      config: this.configManager.getConfigSummary(),
      eventStats: this.eventBus.getEventStats(),
    };
  }

  /**
   * Create Winston logger
   */
  private createLogger(): winston.Logger {
    const logLevel = process.env["LOG_LEVEL"] || "info";
    const logFile = process.env["LOG_FILE_PATH"] || "./logs/trading-bot.log";

    return winston.createLogger({
      level: logLevel,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.json()
      ),
      defaultMeta: { service: "ccxt-trading-bot" },
      transports: [
        new winston.transports.File({
          filename: logFile,
          maxsize: 10 * 1024 * 1024, // 10MB
          maxFiles: 5,
        }),
        new winston.transports.Console({
          format: winston.format.combine(
            winston.format.colorize(),
            winston.format.simple()
          ),
        }),
      ],
    });
  }

  /**
   * Setup event listeners
   */
  private setupEventListeners(): void {
    // Listen for critical events
    this.eventBus.on("connection_lost", (event) => {
      this.logger.warn("Exchange connection lost", event.data);
    });

    this.eventBus.on("connection_restored", (event) => {
      this.logger.info("Exchange connection restored", event.data);
    });

    this.eventBus.on("order_filled", (event) => {
      this.logger.info("Order filled", {
        orderId: event.data.order.id,
        symbol: event.data.order.symbol,
        side: event.data.order.side,
        amount: event.data.order.filled,
        price: event.data.order.average,
      });
    });

    this.eventBus.on("module_error", (event) => {
      this.logger.error("Module error", event.data);
    });
  }

  /**
   * Setup graceful shutdown
   */
  private setupGracefulShutdown(): void {
    const shutdown = async (signal: string) => {
      this.logger.info(`Received ${signal}, shutting down gracefully...`);

      try {
        await this.stop();
        process.exit(0);
      } catch (error) {
        this.logger.error("Error during shutdown", {
          error: (error as Error).message,
        });
        process.exit(1);
      }
    };

    process.on("SIGTERM", () => shutdown("SIGTERM"));
    process.on("SIGINT", () => shutdown("SIGINT"));

    process.on("uncaughtException", (error) => {
      this.logger.error("Uncaught exception", {
        error: error.message,
        stack: error.stack,
      });
      shutdown("uncaughtException");
    });

    process.on("unhandledRejection", (reason) => {
      this.logger.error("Unhandled rejection", {
        reason: String(reason),
      });
      shutdown("unhandledRejection");
    });
  }

  /**
   * Subscribe to market data for configured strategies
   */
  private async subscribeToMarketData(): Promise<void> {
    if (!this.marketDataManager) {
      throw new Error("Market data manager not initialized");
    }

    const strategies = this.configManager.getEnabledStrategies();

    for (const strategy of strategies) {
      this.logger.info(`Subscribing to market data for ${strategy.symbol}`);

      // Subscribe to ticker updates
      await this.marketDataManager.subscribeTicker(strategy.symbol);

      // Subscribe to OHLCV data for the strategy timeframe
      await this.marketDataManager.subscribeOHLCV(
        strategy.symbol,
        strategy.timeframe as any
      );

      // Subscribe to order book for better entry/exit prices
      await this.marketDataManager.subscribeOrderBook(strategy.symbol);
    }
  }

  /**
   * Log system status
   */
  private logSystemStatus(): void {
    const status = this.getStatus();

    this.logger.info("System Status", {
      running: status["running"],
      modules: Object.keys(status["modules"]).map((name) => ({
        name,
        running: status["modules"][name].running,
        initialized: status["modules"][name].initialized,
      })),
      strategies: this.configManager.getEnabledStrategies().map((s) => ({
        name: s.name,
        symbol: s.symbol,
        timeframe: s.timeframe,
      })),
    });
  }

  private startTime?: number;
}

/**
 * Main application entry point
 */
async function main(): Promise<void> {
  const bot = new TradingBot();

  try {
    await bot.initialize();
    await bot.start();

    // Keep the process running
    process.stdin.resume();
  } catch (error) {
    console.error("Failed to start trading bot:", error);
    process.exit(1);
  }
}

// Start the application if this file is run directly
if (require.main === module) {
  main().catch((error) => {
    console.error("Unhandled error in main:", error);
    process.exit(1);
  });
}

export { TradingBot };
