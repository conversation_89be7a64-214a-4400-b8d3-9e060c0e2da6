{"name": "ccxt-bot", "version": "1.0.0", "description": "Comprehensive Node.js cryptocurrency trading bot using CCXT for Binance", "main": "dist/index.js", "types": "dist/index.d.ts", "license": "MIT", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "dev:watch": "nodemon --exec ts-node src/index.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean"}, "keywords": ["cryptocurrency", "trading", "bot", "ccxt", "binance", "typescript", "algorithmic-trading"], "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "dependencies": {"ccxt": "^4.4.99", "crypto-js": "^4.2.0", "dotenv": "^17.2.1", "fs": "^0.0.1-security", "joi": "^18.0.0", "path": "^0.12.7", "uuid": "^11.1.0", "winston": "^3.17.0", "ws": "^8.18.3"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/jest": "^30.0.0", "@types/node": "^24.2.0", "@types/uuid": "^10.0.0", "@types/winston": "^2.4.4", "@types/ws": "^8.18.1", "jest": "^30.0.5", "nodemon": "^3.1.10", "ts-jest": "^29.4.1", "ts-node": "^10.9.2", "typescript": "^5.9.2"}}