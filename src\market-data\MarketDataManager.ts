import { Ticker, OrderBook, Trade, OHLCV } from "ccxt";
import { BaseModule } from "../core/BaseModule";
import { EventBus, EventFactory } from "../core/EventBus";
import { ExchangeConnector } from "../connectivity/ExchangeConnector";
import { MarketData, CandleData, TimeFrame } from "../types";
import { Logger } from "winston";

/**
 * Market data cache for storing recent data
 */
class MarketDataCache {
  private tickers: Map<string, Ticker> = new Map();
  private orderBooks: Map<string, OrderBook> = new Map();
  private trades: Map<string, Trade[]> = new Map();
  private ohlcv: Map<string, Map<TimeFrame, OHLCV[]>> = new Map();
  private maxTradesPerSymbol: number = 100;
  private maxCandlesPerSymbol: number = 1000;

  /**
   * Update ticker data
   */
  public updateTicker(symbol: string, ticker: Ticker): void {
    this.tickers.set(symbol, ticker);
  }

  /**
   * Update order book data
   */
  public updateOrderBook(symbol: string, orderBook: OrderBook): void {
    this.orderBooks.set(symbol, orderBook);
  }

  /**
   * Update trades data
   */
  public updateTrades(symbol: string, newTrades: Trade[]): void {
    const existingTrades = this.trades.get(symbol) || [];
    const allTrades = [...existingTrades, ...newTrades];

    // Keep only recent trades
    const recentTrades = allTrades
      .sort((a, b) => (b.timestamp || 0) - (a.timestamp || 0))
      .slice(0, this.maxTradesPerSymbol);

    this.trades.set(symbol, recentTrades);
  }

  /**
   * Update OHLCV data
   */
  public updateOHLCV(
    symbol: string,
    timeframe: TimeFrame,
    newCandles: OHLCV[]
  ): void {
    if (!this.ohlcv.has(symbol)) {
      this.ohlcv.set(symbol, new Map());
    }

    const symbolData = this.ohlcv.get(symbol)!;
    const existingCandles = symbolData.get(timeframe) || [];

    // Merge and deduplicate candles
    const candleMap = new Map<number, OHLCV>();

    // Add existing candles
    existingCandles.forEach((candle) => {
      candleMap.set(candle[0] || 0, candle);
    });

    // Add new candles (will overwrite existing ones with same timestamp)
    newCandles.forEach((candle) => {
      candleMap.set(candle[0] || 0, candle);
    });

    // Convert back to array and sort by timestamp
    const allCandles = Array.from(candleMap.values())
      .sort((a, b) => (a[0] || 0) - (b[0] || 0))
      .slice(-this.maxCandlesPerSymbol);

    symbolData.set(timeframe, allCandles);
  }

  /**
   * Get ticker data
   */
  public getTicker(symbol: string): Ticker | undefined {
    return this.tickers.get(symbol);
  }

  /**
   * Get order book data
   */
  public getOrderBook(symbol: string): OrderBook | undefined {
    return this.orderBooks.get(symbol);
  }

  /**
   * Get trades data
   */
  public getTrades(symbol: string, limit?: number): Trade[] {
    const trades = this.trades.get(symbol) || [];
    return limit ? trades.slice(0, limit) : trades;
  }

  /**
   * Get OHLCV data
   */
  public getOHLCV(
    symbol: string,
    timeframe: TimeFrame,
    limit?: number
  ): OHLCV[] {
    const symbolData = this.ohlcv.get(symbol);
    if (!symbolData) return [];

    const candles = symbolData.get(timeframe) || [];
    return limit ? candles.slice(-limit) : candles;
  }

  /**
   * Get all cached symbols
   */
  public getCachedSymbols(): string[] {
    const symbols = new Set<string>();

    this.tickers.forEach((_, symbol) => symbols.add(symbol));
    this.orderBooks.forEach((_, symbol) => symbols.add(symbol));
    this.trades.forEach((_, symbol) => symbols.add(symbol));
    this.ohlcv.forEach((_, symbol) => symbols.add(symbol));

    return Array.from(symbols);
  }

  /**
   * Clear cache for a symbol
   */
  public clearSymbol(symbol: string): void {
    this.tickers.delete(symbol);
    this.orderBooks.delete(symbol);
    this.trades.delete(symbol);
    this.ohlcv.delete(symbol);
  }

  /**
   * Clear all cache
   */
  public clearAll(): void {
    this.tickers.clear();
    this.orderBooks.clear();
    this.trades.clear();
    this.ohlcv.clear();
  }

  /**
   * Get cache statistics
   */
  public getStats(): Record<string, any> {
    return {
      tickers: this.tickers.size,
      orderBooks: this.orderBooks.size,
      trades: this.trades.size,
      ohlcvSymbols: this.ohlcv.size,
      totalOhlcvTimeframes: Array.from(this.ohlcv.values()).reduce(
        (total, symbolData) => total + symbolData.size,
        0
      ),
    };
  }
}

/**
 * Market data manager for real-time data feeds
 */
export class MarketDataManager extends BaseModule {
  private exchangeConnector: ExchangeConnector;
  private cache: MarketDataCache;
  private subscriptions: Map<string, Set<string>> = new Map(); // symbol -> set of data types
  private watchIntervals: Map<string, NodeJS.Timeout> = new Map();
  private isWatching: boolean = false;
  private watchDelay: number = 1000; // 1 second between watch cycles

  constructor(
    eventBus: EventBus,
    logger: Logger,
    exchangeConnector: ExchangeConnector
  ) {
    super("MarketDataManager", eventBus, logger);
    this.exchangeConnector = exchangeConnector;
    this.cache = new MarketDataCache();
  }

  /**
   * Initialize the market data manager
   */
  protected async onInitialize(): Promise<void> {
    // Verify exchange connector is available
    if (!this.exchangeConnector.isRunning()) {
      throw new Error(
        "Exchange connector must be running before initializing market data manager"
      );
    }

    this.logger.info("Market data manager initialized");
  }

  /**
   * Start the market data manager
   */
  protected async onStart(): Promise<void> {
    this.isWatching = true;
    this.logger.info("Market data manager started");
  }

  /**
   * Stop the market data manager
   */
  protected async onStop(): Promise<void> {
    this.isWatching = false;

    // Clear all watch intervals
    this.watchIntervals.forEach((interval) => clearInterval(interval));
    this.watchIntervals.clear();

    // Clear subscriptions
    this.subscriptions.clear();

    this.logger.info("Market data manager stopped");
  }

  /**
   * Get module-specific status
   */
  protected getModuleSpecificStatus(): Record<string, any> {
    return {
      isWatching: this.isWatching,
      subscriptions: Object.fromEntries(
        Array.from(this.subscriptions.entries()).map(([symbol, types]) => [
          symbol,
          Array.from(types),
        ])
      ),
      cache: this.cache.getStats(),
      activeWatchers: this.watchIntervals.size,
    };
  }

  /**
   * Subscribe to ticker updates for a symbol
   */
  public async subscribeTicker(symbol: string): Promise<void> {
    this.addSubscription(symbol, "ticker");

    if (!this.watchIntervals.has(`ticker_${symbol}`)) {
      const interval = setInterval(async () => {
        if (this.isWatching) {
          await this.watchTicker(symbol);
        }
      }, this.watchDelay);

      this.watchIntervals.set(`ticker_${symbol}`, interval);
    }

    this.logger.debug(`Subscribed to ticker updates for ${symbol}`);
  }

  /**
   * Subscribe to order book updates for a symbol
   */
  public async subscribeOrderBook(symbol: string): Promise<void> {
    this.addSubscription(symbol, "orderbook");

    if (!this.watchIntervals.has(`orderbook_${symbol}`)) {
      const interval = setInterval(async () => {
        if (this.isWatching) {
          await this.watchOrderBook(symbol);
        }
      }, this.watchDelay);

      this.watchIntervals.set(`orderbook_${symbol}`, interval);
    }

    this.logger.debug(`Subscribed to order book updates for ${symbol}`);
  }

  /**
   * Subscribe to trade updates for a symbol
   */
  public async subscribeTrades(symbol: string): Promise<void> {
    this.addSubscription(symbol, "trades");

    if (!this.watchIntervals.has(`trades_${symbol}`)) {
      const interval = setInterval(async () => {
        if (this.isWatching) {
          await this.watchTrades(symbol);
        }
      }, this.watchDelay);

      this.watchIntervals.set(`trades_${symbol}`, interval);
    }

    this.logger.debug(`Subscribed to trade updates for ${symbol}`);
  }

  /**
   * Subscribe to OHLCV updates for a symbol and timeframe
   */
  public async subscribeOHLCV(
    symbol: string,
    timeframe: TimeFrame
  ): Promise<void> {
    this.addSubscription(symbol, `ohlcv_${timeframe}`);

    const key = `ohlcv_${symbol}_${timeframe}`;
    if (!this.watchIntervals.has(key)) {
      const interval = setInterval(async () => {
        if (this.isWatching) {
          await this.watchOHLCV(symbol, timeframe);
        }
      }, this.watchDelay * 5); // OHLCV updates less frequently

      this.watchIntervals.set(key, interval);
    }

    this.logger.debug(`Subscribed to OHLCV updates for ${symbol} ${timeframe}`);
  }

  /**
   * Unsubscribe from all data for a symbol
   */
  public unsubscribe(symbol: string): void {
    this.subscriptions.delete(symbol);

    // Clear related intervals
    const keysToRemove: string[] = [];
    this.watchIntervals.forEach((_, key) => {
      if (key.includes(symbol)) {
        clearInterval(this.watchIntervals.get(key)!);
        keysToRemove.push(key);
      }
    });

    keysToRemove.forEach((key) => this.watchIntervals.delete(key));

    // Clear cache for symbol
    this.cache.clearSymbol(symbol);

    this.logger.debug(`Unsubscribed from all data for ${symbol}`);
  }

  /**
   * Get current ticker data
   */
  public getTicker(symbol: string): Ticker | undefined {
    return this.cache.getTicker(symbol);
  }

  /**
   * Get current order book data
   */
  public getOrderBook(symbol: string): OrderBook | undefined {
    return this.cache.getOrderBook(symbol);
  }

  /**
   * Get recent trades
   */
  public getTrades(symbol: string, limit?: number): Trade[] {
    return this.cache.getTrades(symbol, limit);
  }

  /**
   * Get OHLCV data
   */
  public getOHLCV(
    symbol: string,
    timeframe: TimeFrame,
    limit?: number
  ): OHLCV[] {
    return this.cache.getOHLCV(symbol, timeframe, limit);
  }

  /**
   * Get market data summary for a symbol
   */
  public getMarketData(symbol: string): MarketData | null {
    const ticker = this.cache.getTicker(symbol);
    const orderBook = this.cache.getOrderBook(symbol);
    const trades = this.cache.getTrades(symbol, 10);
    const ohlcv = this.cache.getOHLCV(symbol, "1m", 100);

    if (!ticker) {
      return null;
    }

    return {
      ticker,
      orderBook: orderBook || {
        symbol,
        bids: [],
        asks: [],
        timestamp: Date.now(),
        datetime: new Date().toISOString(),
        nonce: undefined,
      },
      trades,
      ohlcv,
      timestamp: Date.now(),
    };
  }

  /**
   * Watch ticker updates
   */
  private async watchTicker(symbol: string): Promise<void> {
    try {
      const proExchange = this.exchangeConnector.getProExchange();
      const ticker = await proExchange.watchTicker(symbol);

      this.cache.updateTicker(symbol, ticker);

      const event = EventFactory.createMarketDataEvent(
        "ticker",
        symbol,
        ticker,
        this.name
      );
      this.eventBus.emitTradingEvent(event);
    } catch (error) {
      this.handleError(error as Error, `watchTicker_${symbol}`);
    }
  }

  /**
   * Watch order book updates
   */
  private async watchOrderBook(symbol: string): Promise<void> {
    try {
      const proExchange = this.exchangeConnector.getProExchange();
      const orderBook = await proExchange.watchOrderBook(symbol);

      this.cache.updateOrderBook(symbol, orderBook);

      const event = EventFactory.createMarketDataEvent(
        "orderbook",
        symbol,
        orderBook,
        this.name
      );
      this.eventBus.emitTradingEvent(event);
    } catch (error) {
      this.handleError(error as Error, `watchOrderBook_${symbol}`);
    }
  }

  /**
   * Watch trade updates
   */
  private async watchTrades(symbol: string): Promise<void> {
    try {
      const proExchange = this.exchangeConnector.getProExchange();
      const trades = await proExchange.watchTrades(symbol);

      this.cache.updateTrades(symbol, trades);

      const event = EventFactory.createMarketDataEvent(
        "trade",
        symbol,
        trades,
        this.name
      );
      this.eventBus.emitTradingEvent(event);
    } catch (error) {
      this.handleError(error as Error, `watchTrades_${symbol}`);
    }
  }

  /**
   * Watch OHLCV updates
   */
  private async watchOHLCV(
    symbol: string,
    timeframe: TimeFrame
  ): Promise<void> {
    try {
      const proExchange = this.exchangeConnector.getProExchange();
      const ohlcv = await proExchange.watchOHLCV(symbol, timeframe);

      this.cache.updateOHLCV(symbol, timeframe, ohlcv);

      const event = EventFactory.createMarketDataEvent(
        "ohlcv",
        symbol,
        { timeframe, data: ohlcv },
        this.name
      );
      this.eventBus.emitTradingEvent(event);
    } catch (error) {
      this.handleError(error as Error, `watchOHLCV_${symbol}_${timeframe}`);
    }
  }

  /**
   * Add subscription
   */
  private addSubscription(symbol: string, dataType: string): void {
    if (!this.subscriptions.has(symbol)) {
      this.subscriptions.set(symbol, new Set());
    }
    this.subscriptions.get(symbol)!.add(dataType);
  }

  /**
   * Convert OHLCV to CandleData format
   */
  public convertToCandles(ohlcv: OHLCV[]): CandleData[] {
    return ohlcv.map((candle) => ({
      timestamp: candle[0] || 0,
      open: candle[1] || 0,
      high: candle[2] || 0,
      low: candle[3] || 0,
      close: candle[4] || 0,
      volume: candle[5] || 0,
    }));
  }

  /**
   * Get cache statistics
   */
  public getCacheStats(): Record<string, any> {
    return this.cache.getStats();
  }

  /**
   * Clear cache
   */
  public clearCache(): void {
    this.cache.clearAll();
    this.logger.info("Market data cache cleared");
  }
}
