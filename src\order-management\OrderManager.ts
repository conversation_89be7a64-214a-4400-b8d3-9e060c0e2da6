import { Order } from "ccxt";
import { v4 as uuidv4 } from "uuid";
import { BaseModule } from "../core/BaseModule";
import { EventBus, EventFactory } from "../core/EventBus";
import { ExchangeConnector } from "../connectivity/ExchangeConnector";
import { OrderRequest, OrderResult, Position, OrderStatus } from "../types";
import { Logger } from "winston";

/**
 * Order tracking and management
 */
class OrderTracker {
  private orders: Map<string, OrderResult> = new Map();
  private ordersBySymbol: Map<string, Set<string>> = new Map();
  private ordersByStatus: Map<OrderStatus, Set<string>> = new Map();

  /**
   * Add order to tracking
   */
  public addOrder(order: OrderResult): void {
    this.orders.set(order.id, order);

    // Track by symbol
    if (!this.ordersBySymbol.has(order.symbol)) {
      this.ordersBySymbol.set(order.symbol, new Set());
    }
    this.ordersBySymbol.get(order.symbol)!.add(order.id);

    // Track by status
    if (!this.ordersByStatus.has(order.status as OrderStatus)) {
      this.ordersByStatus.set(order.status as OrderStatus, new Set());
    }
    this.ordersByStatus.get(order.status as OrderStatus)!.add(order.id);
  }

  /**
   * Update order status
   */
  public updateOrder(
    orderId: string,
    updates: Partial<OrderResult>
  ): OrderResult | null {
    const order = this.orders.get(orderId);
    if (!order) return null;

    const oldStatus = order.status as OrderStatus;
    const updatedOrder = { ...order, ...updates };

    this.orders.set(orderId, updatedOrder);

    // Update status tracking if status changed
    if (updates.status && updates.status !== oldStatus) {
      this.ordersByStatus.get(oldStatus)?.delete(orderId);

      if (!this.ordersByStatus.has(updates.status as OrderStatus)) {
        this.ordersByStatus.set(updates.status as OrderStatus, new Set());
      }
      this.ordersByStatus.get(updates.status as OrderStatus)!.add(orderId);
    }

    return updatedOrder;
  }

  /**
   * Get order by ID
   */
  public getOrder(orderId: string): OrderResult | undefined {
    return this.orders.get(orderId);
  }

  /**
   * Get orders by symbol
   */
  public getOrdersBySymbol(symbol: string): OrderResult[] {
    const orderIds = this.ordersBySymbol.get(symbol) || new Set();
    return Array.from(orderIds)
      .map((id) => this.orders.get(id))
      .filter((order) => order !== undefined) as OrderResult[];
  }

  /**
   * Get orders by status
   */
  public getOrdersByStatus(status: OrderStatus): OrderResult[] {
    const orderIds = this.ordersByStatus.get(status) || new Set();
    return Array.from(orderIds)
      .map((id) => this.orders.get(id))
      .filter((order) => order !== undefined) as OrderResult[];
  }

  /**
   * Get all orders
   */
  public getAllOrders(): OrderResult[] {
    return Array.from(this.orders.values());
  }

  /**
   * Remove order from tracking
   */
  public removeOrder(orderId: string): boolean {
    const order = this.orders.get(orderId);
    if (!order) return false;

    this.orders.delete(orderId);
    this.ordersBySymbol.get(order.symbol)?.delete(orderId);
    this.ordersByStatus.get(order.status as OrderStatus)?.delete(orderId);

    return true;
  }

  /**
   * Get tracking statistics
   */
  public getStats(): Record<string, any> {
    const statusCounts: Record<string, number> = {};
    this.ordersByStatus.forEach((orderIds, status) => {
      statusCounts[status] = orderIds.size;
    });

    return {
      totalOrders: this.orders.size,
      ordersByStatus: statusCounts,
      symbolsTracked: this.ordersBySymbol.size,
    };
  }

  /**
   * Clear completed orders older than specified time
   */
  public cleanupOldOrders(maxAgeMs: number = 24 * 60 * 60 * 1000): number {
    const now = Date.now();
    let removedCount = 0;

    for (const [orderId, order] of this.orders) {
      const isCompleted = [
        "closed",
        "canceled",
        "expired",
        "rejected",
      ].includes(order.status as string);
      const isOld = now - order.timestamp > maxAgeMs;

      if (isCompleted && isOld) {
        this.removeOrder(orderId);
        removedCount++;
      }
    }

    return removedCount;
  }
}

/**
 * Position tracking and management
 */
class PositionTracker {
  private positions: Map<string, Position> = new Map();

  /**
   * Update position from order fill
   */
  public updatePositionFromOrder(order: OrderResult): Position {
    const positionId = `${order.symbol}_${order.side}`;
    let position = this.positions.get(positionId);

    if (!position) {
      position = {
        id: positionId,
        symbol: order.symbol,
        side: order.side === "buy" ? "long" : "short",
        size: 0,
        entryPrice: 0,
        currentPrice: order.price || 0,
        unrealizedPnl: 0,
        realizedPnl: 0,
        timestamp: order.timestamp,
      };
    }

    const fillAmount = order.filled || 0;
    const fillPrice = order.average || order.price || 0;

    if (order.side === "buy") {
      // Adding to long position
      const newSize = position.size + fillAmount;
      position.entryPrice =
        (position.entryPrice * position.size + fillPrice * fillAmount) /
        newSize;
      position.size = newSize;
    } else {
      // Reducing long position or adding to short
      if (position.side === "long") {
        position.size = Math.max(0, position.size - fillAmount);
        if (position.size === 0) {
          position.realizedPnl +=
            (fillPrice - position.entryPrice) * fillAmount;
        }
      } else {
        // Adding to short position
        const newSize = position.size + fillAmount;
        position.entryPrice =
          (position.entryPrice * position.size + fillPrice * fillAmount) /
          newSize;
        position.size = newSize;
      }
    }

    position.currentPrice = fillPrice;
    position.timestamp = order.timestamp;

    this.positions.set(positionId, position);
    return position;
  }

  /**
   * Update position price
   */
  public updatePositionPrice(symbol: string, price: number): void {
    for (const position of this.positions.values()) {
      if (position.symbol === symbol) {
        position.currentPrice = price;

        // Calculate unrealized PnL
        if (position.size > 0) {
          if (position.side === "long") {
            position.unrealizedPnl =
              (price - position.entryPrice) * position.size;
          } else {
            position.unrealizedPnl =
              (position.entryPrice - price) * position.size;
          }
        }
      }
    }
  }

  /**
   * Get position for symbol
   */
  public getPosition(
    symbol: string,
    side?: "long" | "short"
  ): Position | undefined {
    if (side) {
      return this.positions.get(
        `${symbol}_${side === "long" ? "buy" : "sell"}`
      );
    }

    // Return the position with the largest size
    const positions = Array.from(this.positions.values()).filter(
      (p) => p.symbol === symbol && p.size > 0
    );

    return positions.reduce(
      (largest, current) =>
        current.size > (largest?.size || 0) ? current : largest,
      undefined as Position | undefined
    );
  }

  /**
   * Get all positions
   */
  public getAllPositions(): Position[] {
    return Array.from(this.positions.values()).filter((p) => p.size > 0);
  }

  /**
   * Close position
   */
  public closePosition(positionId: string): boolean {
    return this.positions.delete(positionId);
  }

  /**
   * Get position statistics
   */
  public getStats(): Record<string, any> {
    const positions = this.getAllPositions();
    const totalUnrealizedPnl = positions.reduce(
      (sum, p) => sum + p.unrealizedPnl,
      0
    );
    const totalRealizedPnl = positions.reduce(
      (sum, p) => sum + p.realizedPnl,
      0
    );

    return {
      totalPositions: positions.length,
      totalUnrealizedPnl,
      totalRealizedPnl,
      totalPnl: totalUnrealizedPnl + totalRealizedPnl,
      symbols: [...new Set(positions.map((p) => p.symbol))],
    };
  }
}

/**
 * Order management system
 */
export class OrderManager extends BaseModule {
  private exchangeConnector: ExchangeConnector;
  private orderTracker: OrderTracker;
  private positionTracker: PositionTracker;
  private orderMonitorInterval?: NodeJS.Timeout;
  private monitorIntervalMs: number = 5000; // 5 seconds
  private paperTradingMode: boolean = false;

  constructor(
    eventBus: EventBus,
    logger: Logger,
    exchangeConnector: ExchangeConnector,
    paperTradingMode: boolean = false
  ) {
    super("OrderManager", eventBus, logger);
    this.exchangeConnector = exchangeConnector;
    this.orderTracker = new OrderTracker();
    this.positionTracker = new PositionTracker();
    this.paperTradingMode = paperTradingMode;
  }

  /**
   * Initialize the order manager
   */
  protected async onInitialize(): Promise<void> {
    if (!this.exchangeConnector.isRunning()) {
      throw new Error(
        "Exchange connector must be running before initializing order manager"
      );
    }

    this.logger.info(
      `Order manager initialized (Paper Trading: ${this.paperTradingMode})`
    );
  }

  /**
   * Start the order manager
   */
  protected async onStart(): Promise<void> {
    // Start order monitoring
    this.orderMonitorInterval = setInterval(() => {
      this.monitorOpenOrders();
    }, this.monitorIntervalMs);

    this.logger.info("Order manager started");
  }

  /**
   * Stop the order manager
   */
  protected async onStop(): Promise<void> {
    if (this.orderMonitorInterval) {
      clearInterval(this.orderMonitorInterval);
      this.orderMonitorInterval = undefined as any;
    }

    this.logger.info("Order manager stopped");
  }

  /**
   * Get module-specific status
   */
  protected getModuleSpecificStatus(): Record<string, any> {
    return {
      paperTradingMode: this.paperTradingMode,
      orderStats: this.orderTracker.getStats(),
      positionStats: this.positionTracker.getStats(),
      monitoringActive: !!this.orderMonitorInterval,
    };
  }

  /**
   * Create a new order
   */
  public async createOrder(orderRequest: OrderRequest): Promise<OrderResult> {
    try {
      this.logger.info("Creating order", {
        symbol: orderRequest.symbol,
        type: orderRequest.type,
        side: orderRequest.side,
        amount: orderRequest.amount,
        price: orderRequest.price,
        paperTrading: this.paperTradingMode,
      });

      let order: OrderResult;

      if (this.paperTradingMode) {
        order = await this.createPaperOrder(orderRequest);
      } else {
        order = await this.createRealOrder(orderRequest);
      }

      // Track the order
      this.orderTracker.addOrder(order);

      // Emit order created event
      const event = EventFactory.createOrderEvent(
        "order_created",
        order.id,
        order,
        this.name
      );
      this.eventBus.emitTradingEvent(event);

      this.logger.info("Order created successfully", {
        orderId: order.id,
        symbol: order.symbol,
        status: order.status,
      });

      return order;
    } catch (error) {
      this.logger.error("Failed to create order", {
        error: (error as Error).message,
        orderRequest,
      });

      // Emit order failed event
      const event = EventFactory.createOrderEvent(
        "order_failed",
        orderRequest.clientOrderId || "unknown",
        { error: (error as Error).message, orderRequest },
        this.name
      );
      this.eventBus.emitTradingEvent(event);

      throw error;
    }
  }

  /**
   * Cancel an order
   */
  public async cancelOrder(
    orderId: string,
    symbol?: string
  ): Promise<OrderResult> {
    try {
      const trackedOrder = this.orderTracker.getOrder(orderId);
      if (!trackedOrder) {
        throw new Error(`Order ${orderId} not found in tracking`);
      }

      let canceledOrder: OrderResult;

      if (this.paperTradingMode) {
        canceledOrder = await this.cancelPaperOrder(orderId);
      } else {
        const exchange = this.exchangeConnector.getExchange();
        const order = await exchange.cancelOrder(
          orderId,
          symbol || trackedOrder.symbol
        );
        canceledOrder = this.convertToOrderResult(order);
      }

      // Update tracking
      this.orderTracker.updateOrder(orderId, { status: "canceled" });

      // Emit order canceled event
      const event = EventFactory.createOrderEvent(
        "order_canceled",
        orderId,
        canceledOrder,
        this.name
      );
      this.eventBus.emitTradingEvent(event);

      this.logger.info("Order canceled successfully", { orderId });
      return canceledOrder;
    } catch (error) {
      this.logger.error("Failed to cancel order", {
        error: (error as Error).message,
        orderId,
      });
      throw error;
    }
  }

  /**
   * Fetch order status
   */
  public async fetchOrder(
    orderId: string,
    symbol?: string
  ): Promise<OrderResult> {
    try {
      const trackedOrder = this.orderTracker.getOrder(orderId);

      if (this.paperTradingMode && trackedOrder) {
        return trackedOrder;
      }

      const exchange = this.exchangeConnector.getExchange();
      const order = await exchange.fetchOrder(
        orderId,
        symbol || trackedOrder?.symbol
      );
      const orderResult = this.convertToOrderResult(order);

      // Update tracking
      this.orderTracker.updateOrder(orderId, orderResult);

      return orderResult;
    } catch (error) {
      this.logger.error("Failed to fetch order", {
        error: (error as Error).message,
        orderId,
      });
      throw error;
    }
  }

  /**
   * Get order by ID
   */
  public getOrder(orderId: string): OrderResult | undefined {
    return this.orderTracker.getOrder(orderId);
  }

  /**
   * Get orders by symbol
   */
  public getOrdersBySymbol(symbol: string): OrderResult[] {
    return this.orderTracker.getOrdersBySymbol(symbol);
  }

  /**
   * Get orders by status
   */
  public getOrdersByStatus(status: OrderStatus): OrderResult[] {
    return this.orderTracker.getOrdersByStatus(status);
  }

  /**
   * Get open orders
   */
  public getOpenOrders(): OrderResult[] {
    return this.orderTracker.getOrdersByStatus("open");
  }

  /**
   * Get position for symbol
   */
  public getPosition(symbol: string): Position | undefined {
    return this.positionTracker.getPosition(symbol);
  }

  /**
   * Get all positions
   */
  public getAllPositions(): Position[] {
    return this.positionTracker.getAllPositions();
  }

  /**
   * Update position prices from market data
   */
  public updatePositionPrices(symbol: string, price: number): void {
    this.positionTracker.updatePositionPrice(symbol, price);
  }

  /**
   * Create real order on exchange
   */
  private async createRealOrder(
    orderRequest: OrderRequest
  ): Promise<OrderResult> {
    const exchange = this.exchangeConnector.getExchange();

    const order = await this.exchangeConnector.executeRequest(async () => {
      return await exchange.createOrder(
        orderRequest.symbol,
        orderRequest.type,
        orderRequest.side,
        orderRequest.amount,
        orderRequest.price,
        undefined, // params
        {
          timeInForce: orderRequest.timeInForce,
          clientOrderId: orderRequest.clientOrderId,
        }
      );
    });

    return this.convertToOrderResult(order);
  }

  /**
   * Create paper order (simulation)
   */
  private async createPaperOrder(
    orderRequest: OrderRequest
  ): Promise<OrderResult> {
    const orderId = orderRequest.clientOrderId || uuidv4();
    const timestamp = Date.now();

    // Simulate order creation
    const order: any = {
      id: orderId,
      clientOrderId: orderRequest.clientOrderId as string,
      symbol: orderRequest.symbol,
      type: orderRequest.type,
      side: orderRequest.side,
      amount: orderRequest.amount,
      price: orderRequest.price || 0,
      filled: 0,
      remaining: orderRequest.amount,
      status: orderRequest.type === "market" ? "closed" : "open",
      timestamp,
      datetime: new Date(timestamp).toISOString(),
      lastTradeTimestamp: undefined,
      average: undefined,
      cost: 0,
      trades: [],
      fee: undefined,
      info: { paperTrading: true },
      executionTime: 100, // Simulate 100ms execution time
      fees: {
        currency: "USDT",
        cost: 0,
        rate: 0.001,
      },
    };

    // For market orders, simulate immediate fill
    if (orderRequest.type === "market") {
      order.filled = order.amount;
      order.remaining = 0;
      order.average = orderRequest.price || 0;
      order.cost = order.filled * (order.average || 0);
    }

    return order;
  }

  /**
   * Cancel paper order
   */
  private async cancelPaperOrder(orderId: string): Promise<OrderResult> {
    const order = this.orderTracker.getOrder(orderId);
    if (!order) {
      throw new Error(`Paper order ${orderId} not found`);
    }

    return {
      ...order,
      status: "canceled",
      remaining: 0,
    };
  }

  /**
   * Convert CCXT order to OrderResult
   */
  private convertToOrderResult(order: Order): OrderResult {
    return {
      ...order,
      executionTime: Date.now() - order.timestamp,
      fees: {
        currency: order.fee?.currency || "USDT",
        cost: order.fee?.cost || 0,
        rate: order.fee?.rate || 0.001,
      },
    };
  }

  /**
   * Monitor open orders for status changes
   */
  private async monitorOpenOrders(): Promise<void> {
    try {
      const openOrders = this.getOpenOrders();

      for (const order of openOrders) {
        try {
          const updatedOrder = await this.fetchOrder(order.id, order.symbol);

          // Check if order status changed
          if (updatedOrder.status !== order.status) {
            this.handleOrderStatusChange(order, updatedOrder);
          }
        } catch (error) {
          this.logger.warn("Failed to monitor order", {
            orderId: order.id,
            error: (error as Error).message,
          });
        }
      }
    } catch (error) {
      this.logger.error("Error monitoring open orders", {
        error: (error as Error).message,
      });
    }
  }

  /**
   * Handle order status changes
   */
  private handleOrderStatusChange(
    oldOrder: OrderResult,
    newOrder: OrderResult
  ): void {
    this.logger.info("Order status changed", {
      orderId: newOrder.id,
      oldStatus: oldOrder.status,
      newStatus: newOrder.status,
      filled: newOrder.filled,
    });

    // Update position if order was filled
    if (newOrder.status === "closed" && newOrder.filled > 0) {
      const position = this.positionTracker.updatePositionFromOrder(newOrder);

      // Emit order filled event
      const event = EventFactory.createOrderEvent(
        "order_filled",
        newOrder.id,
        { order: newOrder, position },
        this.name
      );
      this.eventBus.emitTradingEvent(event);
    }
  }

  /**
   * Cleanup old completed orders
   */
  public cleanupOldOrders(): number {
    return this.orderTracker.cleanupOldOrders();
  }
}
